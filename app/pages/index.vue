<script setup lang="ts">
const appConfig = useAppConfig()

const { products, fetchProducts } = useProducts()

await fetchProducts()

const { data: versions } = await useFetch(computed(() => `https://ungh.cc/repos/${appConfig.repository}/releases`), {
  transform: (data: {
    releases: {
      name?: string
      tag: string
      publishedAt: string
      markdown: string
    }[]
  }) => {
    return data.releases.map(release => ({
      tag: release.tag,
      title: release.name || release.tag,
      date: '今日',
      markdown: release.markdown
    }))
  }
})

const cards = ref([
  {
    title: 'トマト',
    description: '300円/3個',
    icon: 'healthicons:vegetables',
    class: 'lg:col-span-1',
    image: {
      path: '/images/items/tomato.png'
    },
    orientation: 'vertical' as const
  },

  {
    title: 'りんご',
    description: '300円/3個',
    icon: 'healthicons:vegetables',
    class: 'lg:col-span-1',
    image: {
      path: '/images/items/apples.png'
    },
    orientation: 'vertical' as const
  }, {
    title: '人参',
    description: '400円/3個',
    icon: 'healthicons:vegetables',
    class: 'lg:col-span-1',
    image: {
      path: '/images/items/carrots.png'
    },
    orientation: 'vertical' as const
  },
  {
    title: 'Color Mode',
    description: 'Nuxt UI integrates with Nuxt Color Mode to switch between light and dark.',
    icon: 'i-lucide-sun-moon',
    variant: 'soft' as const
  },
  {
    title: 'Icons',
    description: 'Nuxt UI integrates with Nuxt Icon to access over 200,000+ icons from Iconify.',
    icon: 'i-lucide-smile',
    image: {
      path: 'https://ui2.nuxt.com/illustrations/icon-library',
      width: 362,
      height: 184
    },
    class: 'lg:col-span-2',
    orientation: 'horizontal' as const,
    reverse: true
  }
])
</script>

<template>
  <UChangelogVersions
    as="main"
    :indicator-motion="false"
    :ui="{
      root: 'py-16 sm:py-24 lg:py-32',
      indicator: 'inset-y-0'
    }"
  >
    <UChangelogVersion
      v-for="version in versions"
      :key="version.tag"
      v-bind="version"
      :ui="{
        root: 'flex items-start',
        container: 'w-full',
        header: 'pb-4',
        title: 'text-3xl',
        date: 'text-lg text-highlighted font-mono',
        indicator: 'sticky top-0 pt-16 -mt-16 sm:pt-24 sm:-mt-24 lg:pt-32 lg:-mt-32'
      }"
    >
      <template #body>
        {{ products }}
        <UPageGrid>
          <UPageCard
            v-for="(card, index) in cards"
            :key="index"
            v-bind="card"
          >
            <UColorModeImage
              v-if="card.image"
              :light="`${card.image.path}`"
              :dark="`${card.image.path}`"
              :width="card.image.width"
              :height="card.image.height"
              :alt="card.title"
              loading="lazy"
              class="w-full"
            />
          </UPageCard>
        </UPageGrid>
      </template>
    </UChangelogVersion>
  </UChangelogVersions>
</template>
