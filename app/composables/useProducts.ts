export const useProducts = () => {
  const products = ref([]) as Ref<Product[]>

  const fetchProducts = async () => {
    try {
      const nuxtApp = useNuxtApp()
      const { data, error } = await nuxtApp.$supabase.from('products').select()
      if (error) throw error
      products.value = data || []
    } catch (error) {
      console.error('Error fetching products:', error)
    }
  }

  return {
    products,
    fetchProducts
  }
}
